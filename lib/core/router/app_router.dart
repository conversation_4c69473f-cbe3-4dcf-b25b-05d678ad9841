import 'package:flutter/material.dart';

import '../../features/account_settings/view/account_settings_screen.dart';
import '../../features/adjust_stock/view/adjust_stock_screen.dart';
import '../../features/cash_bank/view/cash_bank_screen.dart';
import '../../features/create_item/view/create_item_screen.dart';
import '../../features/create_party/view/create_party_screen.dart';
import '../../features/edit_item/view/edit_item_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/import_export/view/import_export_screen.dart';
import '../../features/item_details/view/item_details_screen.dart';
import '../../features/item_reports/view/item_reports_screen.dart';
import '../../features/party_reports/view/party_reports_screen.dart';
import '../../features/reminder_settings/view/reminder_settings_screen.dart';
import '../../features/reports/view/reports_screen.dart';
import '../../features/settings/view/item_settings_screen.dart';
import '../../features/splash/view/splash_screen.dart';
import '../../features/stock_summary/view/stock_summary_screen.dart';
import 'app_routes.dart';
import 'custom_page_route.dart';

class AppRouter {
  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case SplashRoute.name:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case HomeRoute.name:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      case StockSummaryRoute.name:
        return MaterialPageRoute(builder: (_) => const StockSummaryScreen());
      case ReportsRoute.name:
        return MaterialPageRoute(builder: (_) => const ReportsScreen());
      case CashBankRoute.name:
        return MaterialPageRoute(builder: (_) => const CashBankScreen());
      case ImportExportRoute.name:
        return MaterialPageRoute(builder: (_) => const ImportExportScreen());
      case CreatePartyRoute.name:
        return MaterialPageRoute(builder: (_) => const CreatePartyScreen());
      case AdjustStockRoute.name:
        final item = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(builder: (_) => AdjustStockScreen(item: item));
      case ItemSettingsRoute.name:
        return SlidePageRoute(
          child: const ItemSettingsScreen(),
          settings: settings,
        );
      case CreateItemRoute.name:
        return SlidePageRoute(
          child: const CreateItemScreen(),
          settings: settings,
        );
      case AccountSettingsRoute.name:
        return SlidePageRoute(
          child: const AccountSettingsScreen(),
          settings: settings,
        );
      case ReminderSettingsRoute.name:
        return SlidePageRoute(
          child: const ReminderSettingsScreen(),
          settings: settings,
        );
      case ItemDetailsRoute.name:
        final item = settings.arguments as Map<String, dynamic>;
        return SlidePageRoute(
          child: ItemDetailsScreen(item: item),
          settings: settings,
        );
      case EditItemRoute.name:
        final itemData = settings.arguments as Map<String, dynamic>;
        return SlidePageRoute(
          child: EditItemScreen(itemData: itemData),
          settings: settings,
        );
      case PartyReportsRoute.name:
        return SlidePageRoute(
          child: const PartyReportsScreen(),
          settings: settings,
        );
      case ItemReportsRoute.name:
        return SlidePageRoute(
          child: const ItemReportsScreen(),
          settings: settings,
        );
      default:
        return MaterialPageRoute(
          builder: (_) =>
              const Scaffold(body: Center(child: Text('Page not found'))),
        );
    }
  }
}
