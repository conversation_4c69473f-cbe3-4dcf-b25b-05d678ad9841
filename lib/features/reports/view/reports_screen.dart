import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../../../core/router/app_routes.dart';
import '../bloc/reports_bloc.dart';
import '../bloc/reports_event.dart';
import '../bloc/reports_state.dart';
import '../models/report_item.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ReportsBloc()..add(const LoadReports()),
      child: const ReportsView(),
    );
  }
}

class ReportsView extends StatelessWidget {
  const ReportsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const AppText(
          'Reports',
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Colors.black,
        ),
        centerTitle: false,
      ),
      body: BlocBuilder<ReportsBloc, ReportsState>(
        builder: (context, state) {
          return AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            switchInCurve: Curves.easeInOut,
            switchOutCurve: Curves.easeInOut,
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(
                opacity: animation,
                child: SlideTransition(
                  position:
                      Tween<Offset>(
                        begin: const Offset(0.0, 0.1),
                        end: Offset.zero,
                      ).animate(
                        CurvedAnimation(
                          parent: animation,
                          curve: Curves.easeOutCubic,
                        ),
                      ),
                  child: child,
                ),
              );
            },
            child: _buildStateContent(context, state),
          );
        },
      ),
    );
  }

  Widget _buildStateContent(BuildContext context, ReportsState state) {
    if (state is ReportsLoading) {
      return const Center(
        key: ValueKey('loading'),
        child: CircularProgressIndicator(),
      );
    } else if (state is ReportsError) {
      return Center(
        key: ValueKey('error'),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            AppText(
              state.message,
              fontSize: 16,
              color: Colors.red,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<ReportsBloc>().add(const LoadReports());
              },
              child: const AppText('Retry'),
            ),
          ],
        ),
      );
    } else if (state is ReportsLoaded) {
      return _buildAnimatedLoadedContent(context, state);
    }

    return const SizedBox.shrink(key: ValueKey('empty'));
  }

  Widget _buildAnimatedLoadedContent(
    BuildContext context,
    ReportsLoaded state,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ReportsBloc>().add(const RefreshReports());
      },
      child: SafeArea(
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: AnimationLimiter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: AnimationConfiguration.toStaggeredList(
                duration: const Duration(milliseconds: 400),
                delay: const Duration(milliseconds: 100),
                childAnimationBuilder: (widget) => SlideAnimation(
                  verticalOffset: 30.0,
                  curve: Curves.easeOutCubic,
                  child: FadeInAnimation(
                    curve: Curves.easeOutCubic,
                    child: widget,
                  ),
                ),
                children: [
                  // Popular Section
                  _buildAnimatedSection(
                    context,
                    'Popular',
                    state.popularReports,
                    0,
                  ),

                  // More Section
                  _buildAnimatedSection(context, 'More', state.moreReports, 1),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedSection(
    BuildContext context,
    String title,
    List<ReportItem> reports,
    int sectionIndex,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header with slide animation from left
        AnimationConfiguration.staggeredList(
          position: sectionIndex * 2,
          duration: const Duration(milliseconds: 350),
          child: SlideAnimation(
            horizontalOffset: -30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildSectionHeader(title),
            ),
          ),
        ),

        // Reports List with staggered animation
        _buildAnimatedReportsList(context, reports, sectionIndex),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: AppText(
        title,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: Colors.grey[600],
      ),
    );
  }

  Widget _buildAnimatedReportsList(
    BuildContext context,
    List<ReportItem> reports,
    int sectionIndex,
  ) {
    return AnimationLimiter(
      child: Column(
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 300),
          delay: const Duration(milliseconds: 50),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 20.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(curve: Curves.easeOutCubic, child: widget),
          ),
          children: reports
              .map((report) => _buildReportItem(context, report))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildReportItem(BuildContext context, ReportItem report) {
    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: const BoxDecoration(color: Colors.white),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        dense: true,
        visualDensity: VisualDensity.compact,
        leading: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: report.iconColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(report.icon, color: report.iconColor, size: 18),
        ),
        title: AppText(
          report.title,
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: Colors.black87,
        ),
        subtitle: report.subtitle != null
            ? AppText(report.subtitle!, fontSize: 10, color: Colors.grey[600])
            : null,
        trailing: const Icon(Icons.chevron_right, color: Colors.grey, size: 20),
        onTap: () {
          context.read<ReportsBloc>().add(NavigateToReport(report.id));
          _handleReportNavigation(context, report);
        },
      ),
    );
  }

  void _handleReportNavigation(BuildContext context, ReportItem report) {
    // Handle navigation based on report type
    switch (report.id) {
      case 'stock_summary':
        Navigator.pushNamed(context, StockSummaryRoute.name);
        break;
      case 'bill_wise_profit':
        // TODO: Navigate to bill wise profit screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'sales_summary':
        // TODO: Navigate to sales summary screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'daybook':
        // TODO: Navigate to daybook screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'profit_and_loss':
        // TODO: Navigate to profit and loss screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'party_statement':
        // TODO: Navigate to party statement screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'balance_sheet':
        // TODO: Navigate to balance sheet screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'cash_and_bank':
        Navigator.pushNamed(context, CashBankRoute.name);
        break;
      case 'party_reports':
        Navigator.pushNamed(context, PartyReportsRoute.name);
        break;
      case 'item_reports':
        // TODO: Navigate to item reports screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'gst_reports':
        // TODO: Navigate to GST reports screen
        _showComingSoonDialog(context, report.title);
        break;
      case 'transaction_reports':
        // TODO: Navigate to transaction reports screen
        _showComingSoonDialog(context, report.title);
        break;
      default:
        _showComingSoonDialog(context, report.title);
    }
  }

  void _showComingSoonDialog(BuildContext context, String reportTitle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const AppText(
          'Coming Soon',
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        content: AppText('$reportTitle feature is coming soon!', fontSize: 14),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const AppText(
              'OK',
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }
}
