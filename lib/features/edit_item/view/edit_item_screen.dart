import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/constants/app_colors.dart';
import '../bloc/edit_item_bloc.dart';
import '../bloc/edit_item_event.dart';
import '../bloc/edit_item_state.dart';
import '../models/item.dart';
import '../widgets/edit_pricing_tab.dart';
import '../widgets/edit_stocks_tab.dart';
import '../widgets/edit_other_tab.dart';

class EditItemScreen extends StatelessWidget {
  final Map<String, dynamic> itemData;

  const EditItemScreen({super.key, required this.itemData});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => EditItemBloc()..add(InitializeEditItem(itemData)),
      child: const EditItemView(),
    );
  }
}

class EditItemView extends StatefulWidget {
  const EditItemView({super.key});

  @override
  State<EditItemView> createState() => _EditItemViewState();
}

class _EditItemViewState extends State<EditItemView>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _nameController = TextEditingController();
  final _unitController = TextEditingController();
  final _salesPriceController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _hsnController = TextEditingController();
  final _openingStockController = TextEditingController();
  final _itemDescriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
    ); // Default for product
  }

  void _updateTabController(ItemType itemType) {
    final newLength = _getTabsForItemType(itemType).length;
    if (_tabController.length != newLength) {
      _tabController.dispose();
      _tabController = TabController(length: newLength, vsync: this);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _unitController.dispose();
    _salesPriceController.dispose();
    _purchasePriceController.dispose();
    _hsnController.dispose();
    _openingStockController.dispose();
    _itemDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Dismiss keyboard when tapping outside of text fields
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        body: SafeArea(
          child: BlocListener<EditItemBloc, EditItemState>(
            listener: (context, state) {
              if (state is EditItemSuccess) {
                HapticFeedback.lightImpact();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: AppText(
                      'Item "${state.item.name}" updated successfully!',
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 2),
                  ),
                );
                Navigator.of(context).pop(state.item);
              } else if (state is EditItemError) {
                HapticFeedback.lightImpact();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: AppText(
                      state.message,
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                  ),
                );
              } else if (state is EditItemFormState) {
                // Update tab controller when item type changes
                _updateTabController(state.type);

                // Update text controllers with current state values
                _updateTextControllers(state);
              }
            },
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content
                Expanded(
                  child: BlocBuilder<EditItemBloc, EditItemState>(
                    builder: (context, state) {
                      if (state is EditItemLoading) {
                        return const Center(
                          child: CircularProgressIndicator(
                            color: AppColors.primary,
                          ),
                        );
                      } else if (state is EditItemFormState) {
                        return _buildFormContent(context, state);
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _updateTextControllers(EditItemFormState state) {
    if (_nameController.text != state.name) {
      _nameController.text = state.name;
    }
    if (_unitController.text != state.unit) {
      _unitController.text = state.unit;
    }
    if (_salesPriceController.text != state.salesPrice.toString()) {
      _salesPriceController.text = state.salesPrice == 0.0
          ? ''
          : state.salesPrice.toString();
    }
    if (_purchasePriceController.text != state.purchasePrice.toString()) {
      _purchasePriceController.text = state.purchasePrice == 0.0
          ? ''
          : state.purchasePrice.toString();
    }
    if (_hsnController.text != state.hsn) {
      _hsnController.text = state.hsn;
    }
    if (_openingStockController.text != state.openingStock.toString()) {
      _openingStockController.text = state.openingStock == 0.0
          ? ''
          : state.openingStock.toString();
    }
    if (_itemDescriptionController.text != (state.description ?? '')) {
      _itemDescriptionController.text = state.description ?? '';
    }
  }

  Widget _buildHeader(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        verticalOffset: -30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pop();
                  },
                  style: const ButtonStyle(
                    padding: WidgetStatePropertyAll(EdgeInsets.zero),
                    minimumSize: WidgetStatePropertyAll(Size.zero),
                    visualDensity: VisualDensity.compact,
                  ),
                  icon: Icon(
                    Platform.isIOS
                        ? Icons.arrow_back_ios_new_rounded
                        : Icons.arrow_back,
                    color: Colors.black,
                  ),
                ),
                const Expanded(
                  child: AppText(
                    'Edit Item',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormContent(BuildContext context, EditItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Item Name Field
        AnimationConfiguration.staggeredList(
          position: 1,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildItemNameField(context, state),
            ),
          ),
        ),

        // Item Type Selection
        AnimationConfiguration.staggeredList(
          position: 2,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildItemTypeSelection(context, state),
            ),
          ),
        ),

        // Tab Bar
        AnimationConfiguration.staggeredList(
          position: 3,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildTabBar(state),
            ),
          ),
        ),

        // Tab Content
        Expanded(
          child: AnimationConfiguration.staggeredList(
            position: 4,
            duration: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: _buildTabContent(context, state),
              ),
            ),
          ),
        ),

        // Create Button
        AnimationConfiguration.staggeredList(
          position: 5,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildUpdateButton(context, state),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemNameField(BuildContext context, EditItemFormState state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const AppText(
            'Item Name *',
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _nameController,
            onChanged: (value) =>
                context.read<EditItemBloc>().add(ItemNameChanged(value)),
            decoration: InputDecoration(
              hintText: 'Enter item name',
              hintStyle: const TextStyle(color: Colors.black38, fontSize: 14),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Color(0xFF5A67D8),
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              errorText: state.showValidationErrors
                  ? state.errors['name']
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemTypeSelection(
    BuildContext context,
    EditItemFormState state,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                context.read<EditItemBloc>().add(
                  const ItemTypeChanged(ItemType.product),
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: state.type == ItemType.product
                      ? const Color(0xFF5A67D8)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: state.type == ItemType.product
                        ? const Color(0xFF5A67D8)
                        : Colors.grey.shade300,
                    width: 1,
                  ),
                ),
                child: AppText(
                  'Product',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: state.type == ItemType.product
                      ? Colors.white
                      : Colors.black54,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                context.read<EditItemBloc>().add(
                  const ItemTypeChanged(ItemType.service),
                );
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: state.type == ItemType.service
                      ? const Color(0xFF5A67D8)
                      : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: state.type == ItemType.service
                        ? const Color(0xFF5A67D8)
                        : Colors.grey.shade300,
                    width: 1,
                  ),
                ),
                child: AppText(
                  'Service',
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: state.type == ItemType.service
                      ? Colors.white
                      : Colors.black54,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(EditItemFormState state) {
    final tabs = _getTabsForItemType(state.type);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: tabs.map((tab) => Tab(text: tab)).toList(),
        labelColor: const Color(0xFF5A67D8),
        unselectedLabelColor: Colors.black54,
        labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
        indicator: const UnderlineTabIndicator(
          borderSide: BorderSide(color: Color(0xFF5A67D8), width: 2),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
      ),
    );
  }

  List<String> _getTabsForItemType(ItemType itemType) {
    switch (itemType) {
      case ItemType.product:
        return ['Pricing', 'Stock', 'Other'];
      case ItemType.service:
        return ['Pricing', 'Other'];
    }
  }

  Widget _buildTabContent(BuildContext context, EditItemFormState state) {
    return TabBarView(
      controller: _tabController,
      children: _getTabsForItemType(state.type).map((tab) {
        switch (tab) {
          case 'Pricing':
            return EditPricingTab(
              state: state,
              nameController: _nameController,
              unitController: _unitController,
              salesPriceController: _salesPriceController,
              purchasePriceController: _purchasePriceController,
              hsnController: _hsnController,
            );
          case 'Stock':
            return EditStocksTab(
              state: state,
              openingStockController: _openingStockController,
            );
          case 'Other':
            return EditOtherTab(
              state: state,
              itemDescriptionController: _itemDescriptionController,
            );
          default:
            return const SizedBox.shrink();
        }
      }).toList(),
    );
  }

  Widget _buildUpdateButton(BuildContext context, EditItemFormState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: state.isSubmitting
              ? null
              : () {
                  HapticFeedback.lightImpact();
                  context.read<EditItemBloc>().add(const UpdateItem());
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF5A67D8),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: state.isSubmitting
              ? const SizedBox(
                  height: 20,
                  width: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const AppText(
                  'Update Item',
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
        ),
      ),
    );
  }
}
