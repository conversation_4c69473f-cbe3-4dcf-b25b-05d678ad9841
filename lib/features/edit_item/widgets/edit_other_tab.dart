import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/edit_item_bloc.dart';
import '../bloc/edit_item_event.dart';
import '../bloc/edit_item_state.dart';

class EditOtherTab extends StatelessWidget {
  final EditItemFormState state;
  final TextEditingController itemDescriptionController;

  const EditOtherTab({
    super.key,
    required this.state,
    required this.itemDescriptionController,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(curve: Curves.easeOutCubic, child: widget),
            ),
            children: [
              // Item Images Section
              _buildImagesSection(context),
              const SizedBox(height: 24),

              // Category Field
              _buildCategoryField(context),
              const SizedBox(height: 16),

              // Item Description Field
              _buildTextField(
                context: context,
                controller: itemDescriptionController,
                label: 'Item Description',
                hintText: 'Enter item description',
                maxLines: 3,
                onChanged: (value) => context.read<EditItemBloc>().add(
                  ItemDescriptionChanged(value),
                ),
              ),
              const SizedBox(height: 24),

              // Show in Online Store Toggle
              _buildOnlineStoreToggle(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagesSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const AppText(
            'Item Images',
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
          const SizedBox(height: 12),
          if (state.images != null && state.images!.isNotEmpty) ...[
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: state.images!.length + 1,
                itemBuilder: (context, index) {
                  if (index == state.images!.length) {
                    return _buildAddImageButton(context);
                  }
                  return _buildImageItem(context, state.images![index], index);
                },
              ),
            ),
          ] else ...[
            _buildAddImageButton(context),
          ],
        ],
      ),
    );
  }

  Widget _buildAddImageButton(BuildContext context) {
    return GestureDetector(
      onTap: () => _showImagePicker(context),
      child: Container(
        width: 100,
        height: 100,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300, width: 1),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_photo_alternate, size: 32, color: Color(0xFF5A67D8)),
            SizedBox(height: 4),
            AppText('Add Image', fontSize: 12, color: Color(0xFF5A67D8)),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(BuildContext context, String imagePath, int index) {
    return Container(
      width: 100,
      height: 100,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              imagePath,
              width: 100,
              height: 100,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Colors.grey.shade200,
                child: const Icon(
                  Icons.image_not_supported,
                  color: Colors.grey,
                ),
              ),
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: GestureDetector(
              onTap: () => _removeImage(context, index),
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.close, size: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryField(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Category',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _showCategoryBottomSheet(context),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300, width: 1),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  state.category?.isNotEmpty == true
                      ? state.category!
                      : 'Select category',
                  fontSize: 14,
                  color: state.category?.isNotEmpty == true
                      ? Colors.black87
                      : Colors.black38,
                ),
                const Icon(Icons.keyboard_arrow_down, color: Colors.grey),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hintText,
    required Function(String) onChanged,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: const TextStyle(color: Colors.black38, fontSize: 14),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF5A67D8), width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOnlineStoreToggle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText(
                'Show in Online Store',
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              SizedBox(height: 4),
              AppText(
                'Make this item visible in your online store',
                fontSize: 12,
                color: Colors.black54,
              ),
            ],
          ),
          Switch.adaptive(
            value: state.showInOnlineStore ?? false,
            onChanged: (value) {
              HapticFeedback.lightImpact();
              context.read<EditItemBloc>().add(ShowInOnlineStoreChanged(value));
            },
            activeColor: const Color(0xFF5A67D8),
          ),
        ],
      ),
    );
  }

  void _showImagePicker(BuildContext context) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: const AppText(
                'Add Image',
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt, color: Color(0xFF5A67D8)),
              title: const AppText('Take Photo'),
              onTap: () {
                Navigator.of(context).pop();
                _addImageFromCamera(context);
              },
            ),
            ListTile(
              leading: const Icon(
                Icons.photo_library,
                color: Color(0xFF5A67D8),
              ),
              title: const AppText('Choose from Gallery'),
              onTap: () {
                Navigator.of(context).pop();
                _addImageFromGallery(context);
              },
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _addImageFromCamera(BuildContext context) {
    // Simulate adding image from camera
    final currentImages = List<String>.from(state.images ?? []);
    currentImages.add('https://via.placeholder.com/150');
    context.read<EditItemBloc>().add(ImagesChanged(currentImages));
  }

  void _addImageFromGallery(BuildContext context) {
    // Simulate adding image from gallery
    final currentImages = List<String>.from(state.images ?? []);
    currentImages.add('https://via.placeholder.com/150');
    context.read<EditItemBloc>().add(ImagesChanged(currentImages));
  }

  void _removeImage(BuildContext context, int index) {
    HapticFeedback.lightImpact();
    final currentImages = List<String>.from(state.images ?? []);
    currentImages.removeAt(index);
    context.read<EditItemBloc>().add(ImagesChanged(currentImages));
  }

  void _showCategoryBottomSheet(BuildContext context) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Select Category',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                children:
                    [
                          'Electronics',
                          'Clothing',
                          'Food & Beverages',
                          'Home & Garden',
                          'Sports & Outdoors',
                          'Books & Media',
                          'Health & Beauty',
                          'Automotive',
                        ]
                        .map(
                          (category) => ListTile(
                            title: AppText(category),
                            onTap: () {
                              HapticFeedback.lightImpact();
                              context.read<EditItemBloc>().add(
                                CategoryChanged(category),
                              );
                              Navigator.of(context).pop();
                            },
                          ),
                        )
                        .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
