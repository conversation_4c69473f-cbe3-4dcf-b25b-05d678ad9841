import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/edit_item_bloc.dart';
import '../bloc/edit_item_event.dart';
import '../bloc/edit_item_state.dart';
import '../models/item.dart';

class EditPricingTab extends StatelessWidget {
  final EditItemFormState state;
  final TextEditingController nameController;
  final TextEditingController unitController;
  final TextEditingController salesPriceController;
  final TextEditingController purchasePriceController;
  final TextEditingController hsnController;

  const EditPricingTab({
    super.key,
    required this.state,
    required this.nameController,
    required this.unitController,
    required this.salesPriceController,
    required this.purchasePriceController,
    required this.hsnController,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(curve: Curves.easeOutCubic, child: widget),
            ),
            children: [
              // Unit Field
              _buildTextField(
                context: context,
                controller: unitController,
                label: 'Unit *',
                hintText: 'Select unit',
                onChanged: (value) =>
                    context.read<EditItemBloc>().add(UnitChanged(value)),
                errorText: state.showValidationErrors
                    ? state.errors['unit']
                    : null,
                readOnly: true,
                onTap: () => _showUnitBottomSheet(context),
                suffixIcon: const Icon(Icons.keyboard_arrow_down),
              ),
              const SizedBox(height: 16),

              // Sales Price Field
              _buildTextField(
                context: context,
                controller: salesPriceController,
                label: 'Sales Price',
                hintText: 'Enter sales price',
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final price = double.tryParse(value) ?? 0.0;
                  context.read<EditItemBloc>().add(SalesPriceChanged(price));
                },
                errorText: state.showValidationErrors
                    ? state.errors['salesPrice']
                    : null,
                prefixText: '₹ ',
              ),
              const SizedBox(height: 16),

              // Sales Tax Type
              _buildTaxTypeSelector(
                context: context,
                label: 'Sales Tax Type',
                selectedType: state.salesTaxType,
                onChanged: (taxType) => context.read<EditItemBloc>().add(
                  SalesTaxTypeChanged(taxType),
                ),
              ),
              const SizedBox(height: 16),

              // Purchase Price Field
              _buildTextField(
                context: context,
                controller: purchasePriceController,
                label: 'Purchase Price',
                hintText: 'Enter purchase price',
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final price = double.tryParse(value) ?? 0.0;
                  context.read<EditItemBloc>().add(PurchasePriceChanged(price));
                },
                errorText: state.showValidationErrors
                    ? state.errors['purchasePrice']
                    : null,
                prefixText: '₹ ',
              ),
              const SizedBox(height: 16),

              // Purchase Tax Type
              _buildTaxTypeSelector(
                context: context,
                label: 'Purchase Tax Type',
                selectedType: state.purchaseTaxType,
                onChanged: (taxType) => context.read<EditItemBloc>().add(
                  PurchaseTaxTypeChanged(taxType),
                ),
              ),
              const SizedBox(height: 16),

              // GST Type
              _buildGSTTypeSelector(
                context: context,
                selectedType: state.gstType,
                onChanged: (gstType) =>
                    context.read<EditItemBloc>().add(GSTTypeChanged(gstType)),
              ),
              const SizedBox(height: 16),

              // HSN Code Field
              _buildTextField(
                context: context,
                controller: hsnController,
                label: 'HSN Code',
                hintText: 'Select HSN code',
                onChanged: (value) =>
                    context.read<EditItemBloc>().add(HSNChanged(value)),
                readOnly: true,
                onTap: () => _showHSNBottomSheet(context),
                suffixIcon: const Icon(Icons.keyboard_arrow_down),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hintText,
    required Function(String) onChanged,
    TextInputType? keyboardType,
    String? errorText,
    String? prefixText,
    Widget? suffixIcon,
    bool readOnly = false,
    VoidCallback? onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          onChanged: onChanged,
          readOnly: readOnly,
          onTap: onTap,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: const TextStyle(color: Colors.black38, fontSize: 14),
            prefixText: prefixText,
            prefixStyle: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            suffixIcon: suffixIcon,
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF5A67D8), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: errorText,
          ),
        ),
      ],
    );
  }

  Widget _buildTaxTypeSelector({
    required BuildContext context,
    required String label,
    required TaxType selectedType,
    required Function(TaxType) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  onChanged(TaxType.withoutTax);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: selectedType == TaxType.withoutTax
                        ? const Color(0xFF5A67D8)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: selectedType == TaxType.withoutTax
                          ? const Color(0xFF5A67D8)
                          : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: AppText(
                    'Without Tax',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: selectedType == TaxType.withoutTax
                        ? Colors.white
                        : Colors.black54,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  onChanged(TaxType.withTax);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: selectedType == TaxType.withTax
                        ? const Color(0xFF5A67D8)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: selectedType == TaxType.withTax
                          ? const Color(0xFF5A67D8)
                          : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: AppText(
                    'With Tax',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: selectedType == TaxType.withTax
                        ? Colors.white
                        : Colors.black54,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGSTTypeSelector({
    required BuildContext context,
    required GSTType selectedType,
    required Function(GSTType) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'GST Type',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  onChanged(GSTType.none);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: selectedType == GSTType.none
                        ? const Color(0xFF5A67D8)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: selectedType == GSTType.none
                          ? const Color(0xFF5A67D8)
                          : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: AppText(
                    'None',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: selectedType == GSTType.none
                        ? Colors.white
                        : Colors.black54,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  onChanged(GSTType.gst5);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: selectedType == GSTType.gst5
                        ? const Color(0xFF5A67D8)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: selectedType == GSTType.gst5
                          ? const Color(0xFF5A67D8)
                          : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: AppText(
                    'GST 5%',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: selectedType == GSTType.gst5
                        ? Colors.white
                        : Colors.black54,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  onChanged(GSTType.gst18);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: selectedType == GSTType.gst18
                        ? const Color(0xFF5A67D8)
                        : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: selectedType == GSTType.gst18
                          ? const Color(0xFF5A67D8)
                          : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: AppText(
                    'GST 18%',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: selectedType == GSTType.gst18
                        ? Colors.white
                        : Colors.black54,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showUnitBottomSheet(BuildContext context) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Select Unit',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                children:
                    [
                          'BOX',
                          'KG',
                          'GRAM',
                          'LITER',
                          'METER',
                          'PIECE',
                          'DOZEN',
                          'PACKET',
                        ]
                        .map(
                          (unit) => ListTile(
                            title: AppText(unit),
                            onTap: () {
                              HapticFeedback.lightImpact();
                              context.read<EditItemBloc>().add(
                                UnitChanged(unit),
                              );
                              Navigator.of(context).pop();
                            },
                          ),
                        )
                        .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showHSNBottomSheet(BuildContext context) {
    HapticFeedback.lightImpact();
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Select HSN Code',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                children:
                    [
                          {'code': '1001', 'description': 'Wheat and meslin'},
                          {'code': '1002', 'description': 'Rye'},
                          {'code': '1003', 'description': 'Barley'},
                          {'code': '1004', 'description': 'Oats'},
                          {'code': '1005', 'description': 'Maize (corn)'},
                        ]
                        .map(
                          (hsn) => ListTile(
                            title: AppText(
                              '${hsn['code']} - ${hsn['description']}',
                            ),
                            onTap: () {
                              HapticFeedback.lightImpact();
                              context.read<EditItemBloc>().add(
                                HSNChanged(hsn['code']!),
                              );
                              Navigator.of(context).pop();
                            },
                          ),
                        )
                        .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
