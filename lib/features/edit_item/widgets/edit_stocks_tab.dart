import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/edit_item_bloc.dart';
import '../bloc/edit_item_event.dart';
import '../bloc/edit_item_state.dart';

class EditStocksTab extends StatelessWidget {
  final EditItemFormState state;
  final TextEditingController openingStockController;

  const EditStocksTab({
    super.key,
    required this.state,
    required this.openingStockController,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(curve: Curves.easeOutCubic, child: widget),
            ),
            children: [
              // Opening Stock Field
              _buildTextField(
                context: context,
                controller: openingStockController,
                label: 'Opening Stock',
                hintText: 'Enter opening stock',
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final stock = double.tryParse(value) ?? 0.0;
                  context.read<EditItemBloc>().add(OpeningStockChanged(stock));
                },
                errorText: state.showValidationErrors
                    ? state.errors['openingStock']
                    : null,
                suffixText: state.unit,
              ),
              const SizedBox(height: 16),

              // As of Date Field
              _buildDateField(
                context: context,
                label: 'As of Date',
                selectedDate: state.asOfDate,
                onChanged: (date) =>
                    context.read<EditItemBloc>().add(AsOfDateChanged(date)),
              ),
              const SizedBox(height: 24),

              // Low Stock Alert Section
              _buildLowStockAlertSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String hintText,
    required Function(String) onChanged,
    TextInputType? keyboardType,
    String? errorText,
    String? suffixText,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hintText,
            hintStyle: const TextStyle(color: Colors.black38, fontSize: 14),
            suffixText: suffixText,
            suffixStyle: const TextStyle(
              color: Colors.black87,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF5A67D8), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            errorText: errorText,
          ),
        ),
      ],
    );
  }

  Widget _buildDateField({
    required BuildContext context,
    required String label,
    required DateTime selectedDate,
    required Function(DateTime) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _showDatePicker(context, selectedDate, onChanged),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300, width: 1),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                AppText(
                  '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                  fontSize: 14,
                  color: Colors.black87,
                ),
                const Icon(
                  Icons.calendar_today,
                  size: 20,
                  color: Color(0xFF5A67D8),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLowStockAlertSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const AppText(
                'Low Stock Alert',
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              Switch.adaptive(
                value: state.lowStockAlert,
                onChanged: (value) {
                  HapticFeedback.lightImpact();
                  context.read<EditItemBloc>().add(LowStockAlertChanged(value));
                },
                activeColor: const Color(0xFF5A67D8),
              ),
            ],
          ),
          if (state.lowStockAlert) ...[
            const SizedBox(height: 16),
            _buildTextField(
              context: context,
              controller: TextEditingController(
                text: state.lowStockQuantity == 0.0
                    ? ''
                    : state.lowStockQuantity.toString(),
              ),
              label: 'Low Stock Quantity',
              hintText: 'Enter low stock quantity',
              keyboardType: TextInputType.number,
              onChanged: (value) {
                final quantity = double.tryParse(value) ?? 0.0;
                context.read<EditItemBloc>().add(
                  LowStockQuantityChanged(quantity),
                );
              },
              errorText: state.showValidationErrors
                  ? state.errors['lowStockQuantity']
                  : null,
              suffixText: state.unit,
            ),
          ],
        ],
      ),
    );
  }

  void _showDatePicker(
    BuildContext context,
    DateTime selectedDate,
    Function(DateTime) onChanged,
  ) {
    HapticFeedback.lightImpact();
    showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF5A67D8),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    ).then((date) {
      if (date != null) {
        HapticFeedback.lightImpact();
        onChanged(date);
      }
    });
  }
}
