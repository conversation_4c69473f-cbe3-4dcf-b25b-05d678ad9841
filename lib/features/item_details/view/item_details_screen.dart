import 'dart:io';

import 'package:el_tooltip/el_tooltip.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../../core/widgets/custom_info_tooltip.dart';
import '../bloc/item_details_bloc.dart';
import '../bloc/item_details_event.dart';
import '../bloc/item_details_state.dart';
import '../../../core/widgets/app_text.dart';
import '../../../core/router/app_routes.dart';

class ItemDetailsScreen extends StatelessWidget {
  final Map<String, dynamic> item;

  const ItemDetailsScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ItemDetailsBloc()..add(LoadItemDetails(item)),
      child: const ItemDetailsView(),
    );
  }
}

class ItemDetailsView extends StatelessWidget {
  const ItemDetailsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: BlocBuilder<ItemDetailsBloc, ItemDetailsState>(
          builder: (context, state) {
            if (state.status == ItemDetailsStatus.loading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state.status == ItemDetailsStatus.error) {
              return Center(
                child: AppText(
                  state.errorMessage ?? 'An error occurred',
                  color: Colors.red,
                ),
              );
            }

            if (state.item == null) {
              return const Center(child: AppText('No item data available'));
            }

            return AnimationLimiter(
              child: Column(
                children: [
                  // Header
                  AnimationConfiguration.staggeredList(
                    position: 0,
                    duration: const Duration(milliseconds: 300),
                    child: SlideAnimation(
                      verticalOffset: -30.0,
                      child: FadeInAnimation(
                        child: _buildHeader(context, state.item!),
                      ),
                    ),
                  ),

                  // Content
                  Expanded(
                    child: AnimationConfiguration.staggeredList(
                      position: 1,
                      duration: const Duration(milliseconds: 300),
                      delay: const Duration(milliseconds: 100),
                      child: SlideAnimation(
                        verticalOffset: 30.0,
                        child: FadeInAnimation(
                          child: _buildContent(context, state),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, Map<String, dynamic> item) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.of(context).pop();
            },
            child: Icon(
              Platform.isIOS
                  ? Icons.arrow_back_ios_new_rounded
                  : Icons.arrow_back,
              size: 20,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          _buildHeaderAction(
            context,
            Icons.share,
            () => context.read<ItemDetailsBloc>().add(const SharePressed()),
          ),
          const SizedBox(width: 8),
          _buildHeaderAction(context, Icons.edit, () async {
            context.read<ItemDetailsBloc>().add(const EditPressed());

            // Navigate to edit item screen
            final result = await Navigator.of(
              context,
            ).pushNamed(EditItemRoute.name, arguments: item);

            // If item was updated successfully, reload the item details
            if (result != null && context.mounted) {
              context.read<ItemDetailsBloc>().add(LoadItemDetails(item));
            }
          }),
          const SizedBox(width: 8),
          _buildHeaderAction(
            context,
            Icons.delete,
            () => context.read<ItemDetailsBloc>().add(const DeletePressed()),
            color: Colors.red,
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderAction(
    BuildContext context,
    IconData icon,
    VoidCallback onTap, {
    Color? color,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(icon, size: 20, color: color ?? Colors.black87),
      ),
    );
  }

  Widget _buildContent(BuildContext context, ItemDetailsState state) {
    return Column(
      children: [
        // Item Info Section
        _buildItemInfoSection(context, state.item!),

        // Tabs
        _buildTabs(context, state),

        // Tab Content
        Expanded(child: _buildTabContent(context, state)),

        // Adjust Stock Button
        _buildAdjustStockButton(context),
      ],
    );
  }

  Widget _buildItemInfoSection(
    BuildContext context,
    Map<String, dynamic> item,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(color: Colors.white),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Item Image Placeholder
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.image, size: 40, color: Colors.grey),
              ),
              const SizedBox(width: 16),

              // Item Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AppText(
                      item['name'] ?? 'Unknown Item',
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    const SizedBox(height: 4),
                    GestureDetector(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        // Navigate to item report
                      },
                      child: const AppText(
                        'View Item Report',
                        fontSize: 14,
                        color: Color(0xFF5A67D8),
                      ),
                    ),
                  ],
                ),
              ),

              // Stock Quantity
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  const AppText(
                    'Stock Quantity',
                    fontSize: 12,
                    color: Colors.black54,
                  ),
                  AppText(
                    '${item['stock']?.toStringAsFixed(1) ?? '0.0'} ${item['unit'] ?? 'PCS'}',
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Price Information
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const AppText(
                      'Sales Price',
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                    AppText(
                      '₹ ${item['salesPrice']?.toStringAsFixed(0) ?? '0'}',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    const AppText(
                      '(With Tax)',
                      fontSize: 10,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const AppText(
                      'Purchase Price',
                      fontSize: 12,
                      color: Colors.black54,
                    ),
                    AppText(
                      '₹ ${item['purchasePrice']?.toStringAsFixed(0) ?? '0'}',
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    const AppText(
                      '(With Tax)',
                      fontSize: 10,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const AppText(
                        'Stock Value',
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                      const SizedBox(width: 4),
                      CustomInfoTooltip(
                        content: 'Without Tax',
                        iconSize: 14,
                        position: ElTooltipPosition.topCenter,
                        distance: 5,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        iconColor: Colors.grey,
                      ),
                    ],
                  ),
                  AppText(
                    '₹ ${((item['stock'] ?? 0) * (item['purchasePrice'] ?? 0)).toStringAsFixed(2)}',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabs(BuildContext context, ItemDetailsState state) {
    final tabs = ['Item Timeline', 'Details', 'Party Wise Prices'];

    return Container(
      decoration: BoxDecoration(color: Colors.white),
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = state.currentTabIndex == index;

          return Expanded(
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                context.read<ItemDetailsBloc>().add(TabChanged(index));
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: isSelected
                          ? const Color(0xFF5A67D8)
                          : Colors.transparent,
                      width: 2,
                    ),
                  ),
                ),
                child: AppText(
                  tab,
                  fontSize: 12,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.black54,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTabContent(BuildContext context, ItemDetailsState state) {
    switch (state.currentTabIndex) {
      case 0:
        return _buildItemTimelineTab(context, state.item!);
      case 1:
        return _buildDetailsTab(context, state.item!);
      case 2:
        return _buildPartyWisePricesTab(context, state.item!);
      default:
        return _buildItemTimelineTab(context, state.item!);
    }
  }

  Widget _buildItemTimelineTab(
    BuildContext context,
    Map<String, dynamic> item,
  ) {
    // Mock timeline data
    final timelineItems = [
      {
        'date': '25-06-2025',
        'type': 'Invoice',
        'reference': 'BN/SL/1',
        'quantity': -1.0,
        'balance': 123.0,
      },
      {
        'date': '25-06-2025',
        'type': 'Opening Stock',
        'reference': '',
        'quantity': 124.0,
        'balance': 124.0,
      },
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      child: ListView.builder(
        itemCount: timelineItems.length,
        itemBuilder: (context, index) {
          final timelineItem = timelineItems[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 30.0,
              child: FadeInAnimation(child: _buildTimelineItem(timelineItem)),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTimelineItem(Map<String, dynamic> timelineItem) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppText(
                  '${timelineItem['date']} | ${timelineItem['reference']}',
                  fontSize: 12,
                  color: Colors.black54,
                ),
                const SizedBox(height: 4),
                AppText(
                  timelineItem['type'],
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              AppText(
                '${timelineItem['quantity'] > 0 ? '+' : ''}${timelineItem['quantity'].toStringAsFixed(1)} BOX',
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: timelineItem['quantity'] > 0 ? Colors.green : Colors.red,
              ),
              AppText(
                '${timelineItem['balance'].toStringAsFixed(1)} BOX',
                fontSize: 12,
                color: Colors.black54,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab(BuildContext context, Map<String, dynamic> item) {
    return Container(
      decoration: BoxDecoration(color: Colors.white),
      child: SingleChildScrollView(
        child: AnimationLimiter(
          child: Column(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 30.0,
                child: FadeInAnimation(child: widget),
              ),
              children: [
                SizedBox(height: 16),
                _buildDetailItem('Item Code', '890126201023'),
                _buildDetailItem('Measuring Unit', item['unit'] ?? 'BOX'),
                _buildDetailItem('Low Stock at', '20.0 BOX'),
                _buildDetailItem('Tax Rate', '12.0%'),
                _buildDetailItem('HSN Code', '--'),
                _buildDetailItem('Item Type', 'Product'),
                _buildDetailItem(
                  'Item Description',
                  item['description'] ?? 'Amul Butter 500gm',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AppText(label, fontSize: 14, color: Colors.black54),
          AppText(
            value,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ],
      ),
    );
  }

  Widget _buildPartyWisePricesTab(
    BuildContext context,
    Map<String, dynamic> item,
  ) {
    return SafeArea(
      child: Column(
        children: [
          // Main content area
          Expanded(
            child: AnimationLimiter(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: AnimationConfiguration.toStaggeredList(
                  duration: const Duration(milliseconds: 300),
                  childAnimationBuilder: (widget) => SlideAnimation(
                    verticalOffset: 20.0,
                    child: FadeInAnimation(child: widget),
                  ),
                  children: [
                    // Large icon with background
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        Icons.receipt_long_outlined,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Title
                    const AppText(
                      'Party Wise Prices',
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 12),

                    // Description
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: AppText(
                        'Set custom Sales Prices for individual Parties',
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Colors.grey[600]!,
                        textAlign: TextAlign.center,
                        maxLines: 2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showAddPartyWisePriceBottomSheet(
    BuildContext context,
    Map<String, dynamic> item,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Expanded(
                      child: AppText(
                        'Add Party Wise Price',
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        HapticFeedback.lightImpact();
                        Navigator.of(context).pop();
                      },
                      icon: const Icon(Icons.close, color: Colors.grey),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Center(
                    child: AppText(
                      'Party Wise Price form coming soon...',
                      fontSize: 16,
                      color: Colors.grey[600],
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAdjustStockButton(BuildContext context) {
    return BlocBuilder<ItemDetailsBloc, ItemDetailsState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                HapticFeedback.lightImpact();
                context.read<ItemDetailsBloc>().add(const AdjustStockPressed());

                // Navigate to adjust stock screen
                final result = await Navigator.of(
                  context,
                ).pushNamed(AdjustStockRoute.name, arguments: state.item);

                // If stock was adjusted successfully, reload the item details
                if (result == true && context.mounted) {
                  context.read<ItemDetailsBloc>().add(
                    LoadItemDetails(state.item!),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF5A67D8),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child:  AppText(
                 'Adjust Stock',
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        );
      },
    );
  }
}
